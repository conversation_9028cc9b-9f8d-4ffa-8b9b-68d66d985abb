import React from 'react';
import { Code2, Database, Layout, Server, Braces, Globe } from 'lucide-react';

interface SkillCardProps {
  icon: React.ReactNode;
  title: string;
  skills: string[];
}

interface SkillCategory {
  icon: React.ReactNode;
  title: string;
  skills: string[];
}

const skillCategories: SkillCategory[] = [
  {
    icon: <Code2 className="w-8 h-8" />,
    title: 'Frontend Development',
    skills: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS']
  },
  {
    icon: <Server className="w-8 h-8" />,
    title: 'Backend Development',
    skills: ['Node.js', 'Python', 'Go', 'RESTful APIs']
  },
  {
    icon: <Database className="w-8 h-8" />,
    title: 'Databases',
    skills: ['PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch']
  },
  {
    icon: <Layout className="w-8 h-8" />,
    title: 'DevOps',
    skills: ['Docker', 'Kubernetes', 'AWS', 'CI/CD']
  },
  {
    icon: <Braces className="w-8 h-8" />,
    title: 'Programming Languages',
    skills: ['JavaScript', 'TypeScript', 'Python', 'Go']
  },
  {
    icon: <Globe className="w-8 h-8" />,
    title: 'Other Skills',
    skills: ['System Design', 'Agile', 'Git', 'Testing']
  }
];

const SkillCard: React.FC<SkillCardProps> = ({ icon, title, skills }) => {
  return (
    <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md dark:shadow-gray-900/20 transition-colors duration-300">
      <div className="flex items-center mb-4">
        <div className="text-blue-600 dark:text-blue-400 mr-3">{icon}</div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{title}</h3>
      </div>
      <ul className="space-y-2">
        {skills.map((skill) => (
          <li key={skill} className="text-gray-600 dark:text-gray-300">{skill}</li>
        ))}
      </ul>
    </div>
  );
};

const SkillsSection: React.FC = () => {
  return (
    <section className="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-300">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-12 text-center">Technical Skills</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {skillCategories.map((category, index) => (
            <SkillCard
              key={index}
              icon={category.icon}
              title={category.title}
              skills={category.skills}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;
