import { Project } from '../components/ProjectCard';

export const projects: Project[] = [
  {
    id: 'xray-detection',
    title: 'X-Ray Detection System',
    description: 'AI-powered diagnostic tool using deep learning to automatically detect pneumonia from chest X-ray images with high accuracy. Built with TensorFlow and Python for medical applications.',
    tags: ['Python', 'TensorFlow', 'Deep Learning', 'Medical AI'],
    image: 'https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/Medical-AI-X-ray_bcln1g.png',
    href: '/projects/xray-detection',
    githubUrl: 'https://github.com/PeretzNiro/Chest-X-Ray-Images-Classification',
    featured: true,
  },
  {
    id: 'netcommhub',
    title: 'NetComm Hub',
    description: 'A robust networked distributed system for group-based client-server communication with a graphical user interface. Implements advanced networking protocols and real-time messaging.',
    tags: ['Java', 'Networking', 'Distributed Systems', 'GUI'],
    image: 'https://images.unsplash.com/photo-**********-b99a580bb7a8?auto=format&fit=crop&w=400&h=240&q=80',
    href: '/projects/netcommhub',
    githubUrl: 'https://github.com/PeretzNiro/NetCommHub',
    featured: true,
  },
  {
    id: 'study-platform',
    title: 'AI Study Platform',
    description: 'An advanced educational platform that transforms lecture materials into summarized content and interactive quizzes using AI. Built with React, TypeScript, and AWS services.',
    tags: ['React', 'TypeScript', 'AWS', 'AI/ML'],
    image: 'https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/AI-driven-educational-platform_ufzllw.png',
    href: '/projects/study-platform',
    githubUrl: 'https://github.com/PeretzNiro/study-summarization-quiz',
    liveUrl: 'https://study-summarization-quiz.vercel.app/',
    featured: true,
  },
  {
    id: 'text-summarization',
    title: 'DFO Text Summarization',
    description: 'Python implementation of extractive text summarization using Derivative-Free Optimization algorithms for scientific papers. Advanced NLP techniques for document processing.',
    tags: ['Python', 'NLP', 'ML', 'Optimization'],
    image: 'https://images.unsplash.com/photo-1456324504439-367cee3b3c32?auto=format&fit=crop&w=400&h=240&q=80',
    href: '/projects/text-summarization',
    githubUrl: 'https://github.com/PeretzNiro/Text-Summarization-Using-DFO',
    featured: true,
  },
];
