import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Navbar, Footer, Breadcrumb } from './components';

// Lazy load all page components
const HomePage = React.lazy(() => import('./pages/HomePage'));
const ProjectsPage = React.lazy(() => import('./pages/ProjectsPage'));
const XRayDetectionPage = React.lazy(() => import('./pages/XRayDetectionPage'));
const NetCommHubPage = React.lazy(() => import('./pages/NetCommHubPage'));
const StudyPlatformPage = React.lazy(() => import('./pages/StudyPlatformPage'));
const TextSummarizationPage = React.lazy(() => import('./pages/TextSummarizationPage'));

// Loading component
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900 transition-colors duration-300">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400"></div>
  </div>
);

function App() {
  return (
    <>
      <Navbar />
      <Breadcrumb />
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/projects" element={<ProjectsPage />} />
          <Route path="/projects/xray-detection" element={<XRayDetectionPage />} />
          <Route path="/projects/netcommhub" element={<NetCommHubPage />} />
          <Route path="/projects/study-platform" element={<StudyPlatformPage />} />
          <Route path="/projects/text-summarization" element={<TextSummarizationPage />} />
        </Routes>
      </Suspense>
      <Footer />
    </>
  );
}

export default App;