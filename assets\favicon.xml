<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4f46e5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Rounded rectangle background -->
  <rect x="2" y="2" width="28" height="28" rx="6" ry="6" fill="url(#gradient)"/>
  
  <!-- Code brackets -->
  <g fill="white" stroke="none">
    <!-- Left bracket < -->
    <path d="M12 10 L8 16 L12 22" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    
    <!-- Forward slash / -->
    <path d="M18 10 L14 22" stroke="white" stroke-width="2.5" stroke-linecap="round" fill="none"/>
    
    <!-- Right bracket > -->
    <path d="M20 10 L24 16 L20 22" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </g>
</svg>